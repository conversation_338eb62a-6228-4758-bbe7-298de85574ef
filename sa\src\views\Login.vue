<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-left">
        <div class="login-logo">
          <img src="../assets/logo.png" alt="Logo" class="logo-image">
          <h1>图书管理系统</h1>
        </div>
        <div class="login-features">
          <div class="feature">
            <el-icon class="feature-icon"><Reading /></el-icon>
            <div class="feature-text">
              <h3>图书管理</h3>
              <p>添加、编辑、删除图书信息</p>
            </div>
          </div>
          <div class="feature">
            <el-icon class="feature-icon"><Tickets /></el-icon>
            <div class="feature-text">
              <h3>借阅管理</h3>
              <p>借阅、归还、续借、预约</p>
            </div>
          </div>
          <div class="feature">
            <el-icon class="feature-icon"><User /></el-icon>
            <div class="feature-text">
              <h3>用户管理</h3>
              <p>注册、登录、权限控制</p>
            </div>
          </div>
        </div>
      </div>

      <div class="login-right">
        <div class="login-form-container">
          <h2 class="login-title">{{ activeTab === 'login' ? '欢迎回来' : '创建账号' }}</h2>
          <p class="login-subtitle">{{ activeTab === 'login' ? '请登录您的账号' : '请填写以下信息注册' }}</p>
          <el-tabs v-model="activeTab" class="login-tabs">
            <el-tab-pane label="登录" name="login">
              <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" label-position="top">
                <el-form-item label="用户名" prop="username">
                  <el-input
                    v-model="loginForm.username"
                    placeholder="请输入用户名"
                  >
                    <template #prefix>
                      <el-icon><User /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item label="密码" prop="password">
                  <el-input
                    v-model="loginForm.password"
                    type="password"
                    placeholder="请输入密码"
                    show-password
                  >
                    <template #prefix>
                      <el-icon><Lock /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                <div class="form-actions">
                  <el-checkbox v-model="rememberMe">记住我</el-checkbox>
                  <a href="#" class="forgot-password">忘记密码?</a>
                </div>
                <el-form-item>
                  <el-button type="primary" @click="handleLogin" class="submit-button">登录</el-button>
                </el-form-item>

                <!-- 第三方登录分割线 -->
                <div class="divider">
                  <span>或</span>
                </div>

                <!-- 钉钉登录按钮 -->
                <el-form-item>
                  <el-button
                    @click="handleDingtalkLogin"
                    class="dingtalk-login-button"
                    :loading="dingtalkLoading"
                  >
                    <el-icon class="dingtalk-icon">
                      <svg viewBox="0 0 1024 1024" width="16" height="16">
                        <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m244.5 669.2c-11.6 5.4-25.9 10.9-42.6 16.5-122.2 41.2-263.4 41.2-385.6 0-16.7-5.6-31-11.1-42.6-16.5C206.4 697.7 157 615.3 157 512s49.4-185.7 128.7-221.2c11.6-5.4 25.9-10.9 42.6-16.5 122.2-41.2 263.4-41.2 385.6 0 16.7 5.6 31 11.1 42.6 16.5C817.6 326.3 867 408.7 867 512s-49.4 185.7-128.5 221.2z" fill="#2196F3"/>
                      </svg>
                    </el-icon>
                    {{ dingtalkLoading ? '正在跳转...' : '钉钉登录' }}
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <el-tab-pane label="注册" name="register">
              <el-form :model="registerForm" :rules="registerRules" ref="registerFormRef" label-position="top">
                <el-form-item label="用户名" prop="username">
                  <el-input
                    v-model="registerForm.username"
                    placeholder="请输入用户名"
                  >
                    <template #prefix>
                      <el-icon><User /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item label="密码" prop="password">
                  <el-input
                    v-model="registerForm.password"
                    type="password"
                    placeholder="请输入密码"
                    show-password
                  >
                    <template #prefix>
                      <el-icon><Lock /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input
                    v-model="registerForm.confirmPassword"
                    type="password"
                    placeholder="请再次输入密码"
                    show-password
                  >
                    <template #prefix>
                      <el-icon><Lock /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleRegister" class="submit-button">注册</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>

          <div class="login-footer">
            <p>© {{ new Date().getFullYear() }} 图书管理系统. 保留所有权利.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import { Reading, Tickets, User, Lock } from '@element-plus/icons-vue'
import dingtalkAuth from '@/services/dingtalkAuth'
import { checkDingtalkSupport, logDingtalkEvent } from '@/utils/dingtalkUtils'

export default {
  name: 'LoginView',
  components: {
    Reading,
    Tickets,
    User,
    Lock
  },
  setup() {
    const router = useRouter()
    const store = useStore()
    const activeTab = ref('login')
    const loginFormRef = ref(null)
    const registerFormRef = ref(null)
    const rememberMe = ref(false)
    const dingtalkLoading = ref(false)

    // 登录表单
    const loginForm = reactive({
      username: '',
      password: ''
    })

    // 注册表单
    const registerForm = reactive({
      username: '',
      password: '',
      confirmPassword: ''
    })

    // 登录表单验证规则
    const loginRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
      ]
    }

    // 注册表单验证规则
    const registerRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请再次输入密码', trigger: 'blur' },
        {
          validator: (_, value, callback) => {
            if (value !== registerForm.password) {
              callback(new Error('两次输入密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    }

    // 登录处理
    const handleLogin = () => {
      loginFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            // 调用Vuex的登录action
            await store.dispatch('auth/login', {
              username: loginForm.username,
              password: loginForm.password
            })

            ElMessage.success('登录成功')
            router.push('/')
          } catch (error) {
            ElMessage.error('登录失败: ' + error.message)
          }
        } else {
          return false
        }
      })
    }

    // 注册处理
    const handleRegister = () => {
      registerFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            // 调用Vuex的注册action
            await store.dispatch('auth/register', {
              username: registerForm.username,
              password: registerForm.password
            })

            ElMessage.success('注册成功，请登录')
            activeTab.value = 'login'
            loginForm.username = registerForm.username
            loginForm.password = ''

            // 清空注册表单
            registerForm.username = ''
            registerForm.password = ''
            registerForm.confirmPassword = ''
          } catch (error) {
            ElMessage.error('注册失败: ' + error.message)
          }
        } else {
          return false
        }
      })
    }

    // 钉钉登录处理
    const handleDingtalkLogin = () => {
      try {
        // 检查钉钉登录支持
        const support = checkDingtalkSupport()
        if (!support.isSupported) {
          ElMessage.error('当前环境不支持钉钉登录')
          return
        }

        if (support.warnings.length > 0) {
          support.warnings.forEach(warning => {
            ElMessage.warning(warning)
          })
        }

        dingtalkLoading.value = true
        logDingtalkEvent('login_button_clicked')

        // 跳转到钉钉授权页面
        dingtalkAuth.redirectToAuth()
      } catch (error) {
        console.error('钉钉登录失败:', error)
        ElMessage.error('钉钉登录失败: ' + error.message)
        logDingtalkEvent('login_error', { error: error.message })
      } finally {
        // 延迟重置loading状态，因为页面会跳转
        setTimeout(() => {
          dingtalkLoading.value = false
        }, 3000)
      }
    }

    return {
      activeTab,
      loginForm,
      registerForm,
      loginRules,
      registerRules,
      loginFormRef,
      registerFormRef,
      rememberMe,
      dingtalkLoading,
      handleLogin,
      handleRegister,
      handleDingtalkLogin
    }
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.login-box {
  display: flex;
  width: 900px;
  height: 600px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.login-left {
  width: 40%;
  background-color: #409EFF;
  color: white;
  padding: 40px;
  display: flex;
  flex-direction: column;
}

.login-logo {
  text-align: center;
  margin-bottom: 40px;
}

.logo-image {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
}

.login-features {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.feature {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.feature-icon {
  font-size: 30px;
  margin-right: 15px;
}

.feature-text h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
}

.feature-text p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

.login-right {
  width: 60%;
  background-color: white;
  padding: 40px;
  display: flex;
  align-items: center;
}

.login-form-container {
  width: 100%;
}

.login-title {
  font-size: 24px;
  margin-bottom: 5px;
  color: #303133;
}

.login-subtitle {
  color: #909399;
  margin-bottom: 10px;
}

.login-tips {
  background-color: #f0f9eb;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 20px;
  border-left: 3px solid #67c23a;
}

.login-tips p {
  margin: 5px 0;
  font-size: 12px;
  color: #606266;
}

.login-tabs {
  margin-bottom: 20px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.forgot-password {
  color: #409EFF;
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

.submit-button {
  width: 100%;
  padding: 12px 0;
  font-size: 16px;
}

/* 第三方登录分割线 */
.divider {
  text-align: center;
  margin: 20px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #DCDFE6;
}

.divider span {
  background-color: white;
  padding: 0 15px;
  color: #909399;
  font-size: 14px;
}

/* 钉钉登录按钮 */
.dingtalk-login-button {
  width: 100%;
  padding: 12px 0;
  font-size: 16px;
  background-color: #2196F3;
  border-color: #2196F3;
  color: white;
}

.dingtalk-login-button:hover {
  background-color: #1976D2;
  border-color: #1976D2;
}

.dingtalk-login-button:focus {
  background-color: #1976D2;
  border-color: #1976D2;
}

.dingtalk-icon {
  margin-right: 8px;
}

.login-footer {
  text-align: center;
  margin-top: 30px;
  color: #909399;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-box {
    flex-direction: column;
    width: 90%;
    height: auto;
  }

  .login-left, .login-right {
    width: 100%;
  }

  .login-left {
    padding: 20px;
  }

  .login-features {
    display: none;
  }
}
</style>
