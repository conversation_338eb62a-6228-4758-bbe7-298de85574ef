<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue项目中的钉钉OAuth2集成示例</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        
        .code-section {
            margin: 20px 0;
        }
        
        .code-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #2196F3;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .highlight {
            color: #68d391;
        }
        
        .comment {
            color: #a0aec0;
        }
        
        .string {
            color: #fbb6ce;
        }
        
        .keyword {
            color: #63b3ed;
        }
        
        .dingtalk-login-btn {
            background: #2196F3;
            border-color: #2196F3;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .dingtalk-login-btn:hover {
            background: #1976D2;
            border-color: #1976D2;
        }
        
        .demo-login-form {
            max-width: 400px;
            margin: 0 auto;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #DCDFE6;
        }
        
        .divider span {
            background-color: white;
            padding: 0 15px;
            color: #909399;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="demo-container">
            <div class="header">
                <h1>🔐 Vue项目中的钉钉OAuth2集成示例</h1>
                <p>展示如何在Vue 3 + Element Plus项目中集成钉钉OAuth2认证</p>
            </div>

            <!-- 演示登录表单 -->
            <el-card class="demo-login-form">
                <template #header>
                    <div style="text-align: center;">
                        <h3>图书管理系统登录</h3>
                    </div>
                </template>
                
                <el-form :model="loginForm" label-position="top">
                    <el-form-item label="用户名">
                        <el-input v-model="loginForm.username" placeholder="请输入用户名">
                            <template #prefix>
                                <el-icon><User /></el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item label="密码">
                        <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" show-password>
                            <template #prefix>
                                <el-icon><Lock /></el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button type="primary" style="width: 100%;" @click="handleLogin">登录</el-button>
                    </el-form-item>
                    
                    <div class="divider">
                        <span>或</span>
                    </div>
                    
                    <el-form-item>
                        <el-button 
                            class="dingtalk-login-btn" 
                            style="width: 100%;" 
                            @click="handleDingtalkLogin"
                            :loading="dingtalkLoading"
                        >
                            <svg viewBox="0 0 1024 1024" width="16" height="16" style="fill: currentColor;">
                                <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m244.5 669.2c-11.6 5.4-25.9 10.9-42.6 16.5-122.2 41.2-263.4 41.2-385.6 0-16.7-5.6-31-11.1-42.6-16.5C206.4 697.7 157 615.3 157 512s49.4-185.7 128.7-221.2c11.6-5.4 25.9-10.9 42.6-16.5 122.2-41.2 263.4-41.2 385.6 0 16.7 5.6 31 11.1 42.6 16.5C817.6 326.3 867 408.7 867 512s-49.4 185.7-128.5 221.2z"/>
                            </svg>
                            {{ dingtalkLoading ? '正在跳转...' : '钉钉登录' }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 代码示例 -->
            <div class="code-section">
                <div class="code-title">📁 1. 钉钉配置文件 (src/config/dingtalkConfig.js)</div>
                <div class="code-block">
<span class="comment">// 钉钉OAuth2配置</span>
<span class="keyword">export const</span> <span class="highlight">DINGTALK_CONFIG</span> = {
  <span class="comment">// 钉钉应用配置</span>
  appId: <span class="string">'your_app_id_here'</span>, <span class="comment">// 请替换为您的钉钉应用AppId</span>
  appSecret: <span class="string">'your_app_secret_here'</span>, <span class="comment">// 请替换为您的钉钉应用AppSecret</span>
  
  <span class="comment">// OAuth2授权相关配置</span>
  authUrl: <span class="string">'https://login.dingtalk.com/oauth2/auth'</span>,
  tokenUrl: <span class="string">'https://api.dingtalk.com/v1.0/oauth2/userAccessToken'</span>,
  userInfoUrl: <span class="string">'https://api.dingtalk.com/v1.0/contact/users/me'</span>,
  
  <span class="comment">// 回调地址配置</span>
  redirectUri: window.location.origin + <span class="string">'/#/dingtalk/callback'</span>,
  
  <span class="comment">// 授权范围</span>
  scope: <span class="string">'openid corpid'</span>,
  responseType: <span class="string">'code'</span>,
  state: <span class="string">'library_system_auth'</span>
}
                </div>
            </div>

            <div class="code-section">
                <div class="code-title">🔧 2. 钉钉认证服务 (src/services/dingtalkAuth.js)</div>
                <div class="code-block">
<span class="keyword">import</span> axios <span class="keyword">from</span> <span class="string">'axios'</span>
<span class="keyword">import</span> { DINGTALK_CONFIG } <span class="keyword">from</span> <span class="string">'@/config/dingtalkConfig'</span>

<span class="keyword">class</span> <span class="highlight">DingtalkAuthService</span> {
  <span class="comment">/**
   * 生成钉钉OAuth2授权URL
   */</span>
  <span class="highlight">generateAuthUrl</span>() {
    <span class="keyword">const</span> params = <span class="keyword">new</span> URLSearchParams({
      client_id: DINGTALK_CONFIG.appId,
      response_type: DINGTALK_CONFIG.responseType,
      scope: DINGTALK_CONFIG.scope,
      redirect_uri: DINGTALK_CONFIG.redirectUri,
      state: DINGTALK_CONFIG.state,
      prompt: <span class="string">'consent'</span>
    })

    <span class="keyword">return</span> `${DINGTALK_CONFIG.authUrl}?${params.toString()}`
  }

  <span class="comment">/**
   * 跳转到钉钉授权页面
   */</span>
  <span class="highlight">redirectToAuth</span>() {
    <span class="keyword">const</span> authUrl = <span class="keyword">this</span>.generateAuthUrl()
    window.location.href = authUrl
  }

  <span class="comment">/**
   * 完整的钉钉OAuth2登录流程
   */</span>
  <span class="keyword">async</span> <span class="highlight">completeLogin</span>(code, state) {
    <span class="comment">// 实现完整的OAuth2流程</span>
    <span class="comment">// 1. 验证state参数</span>
    <span class="comment">// 2. 获取访问令牌</span>
    <span class="comment">// 3. 获取用户信息</span>
    <span class="comment">// 4. 返回结果</span>
  }
}

<span class="keyword">export default new</span> <span class="highlight">DingtalkAuthService</span>()
                </div>
            </div>

            <div class="code-section">
                <div class="code-title">🎯 3. Vue组件中的使用 (src/views/Login.vue)</div>
                <div class="code-block">
<span class="keyword">&lt;template&gt;</span>
  <span class="keyword">&lt;div</span> class=<span class="string">"login-container"</span><span class="keyword">&gt;</span>
    <span class="comment">&lt;!-- 普通登录表单 --&gt;</span>
    <span class="keyword">&lt;el-form</span> :model=<span class="string">"loginForm"</span><span class="keyword">&gt;</span>
      <span class="comment">&lt;!-- 用户名密码输入框 --&gt;</span>
      
      <span class="comment">&lt;!-- 第三方登录分割线 --&gt;</span>
      <span class="keyword">&lt;div</span> class=<span class="string">"divider"</span><span class="keyword">&gt;</span>
        <span class="keyword">&lt;span&gt;</span>或<span class="keyword">&lt;/span&gt;</span>
      <span class="keyword">&lt;/div&gt;</span>
      
      <span class="comment">&lt;!-- 钉钉登录按钮 --&gt;</span>
      <span class="keyword">&lt;el-button</span> 
        @click=<span class="string">"handleDingtalkLogin"</span>
        class=<span class="string">"dingtalk-login-button"</span>
        :loading=<span class="string">"dingtalkLoading"</span>
      <span class="keyword">&gt;</span>
        钉钉登录
      <span class="keyword">&lt;/el-button&gt;</span>
    <span class="keyword">&lt;/el-form&gt;</span>
  <span class="keyword">&lt;/div&gt;</span>
<span class="keyword">&lt;/template&gt;</span>

<span class="keyword">&lt;script&gt;</span>
<span class="keyword">import</span> dingtalkAuth <span class="keyword">from</span> <span class="string">'@/services/dingtalkAuth'</span>

<span class="keyword">export default</span> {
  <span class="highlight">setup</span>() {
    <span class="keyword">const</span> dingtalkLoading = ref(<span class="keyword">false</span>)
    
    <span class="keyword">const</span> <span class="highlight">handleDingtalkLogin</span> = () =&gt; {
      dingtalkLoading.value = <span class="keyword">true</span>
      dingtalkAuth.redirectToAuth()
    }
    
    <span class="keyword">return</span> {
      dingtalkLoading,
      handleDingtalkLogin
    }
  }
}
<span class="keyword">&lt;/script&gt;</span>
                </div>
            </div>

            <div class="code-section">
                <div class="code-title">🔄 4. Vuex状态管理 (src/store/modules/auth.js)</div>
                <div class="code-block">
<span class="comment">// 钉钉登录action</span>
<span class="keyword">async</span> <span class="highlight">dingtalkLogin</span>({ commit }, { dingtalkToken, dingtalkUserInfo }) {
  <span class="keyword">try</span> {
    commit(<span class="string">'SET_LOADING'</span>, <span class="keyword">true</span>)

    <span class="comment">// 验证钉钉用户信息</span>
    <span class="keyword">const</span> validation = validateDingtalkUserInfo(dingtalkUserInfo)
    <span class="keyword">if</span> (!validation.valid) {
      <span class="keyword">throw new</span> Error(<span class="string">'钉钉用户信息不完整'</span>)
    }

    <span class="comment">// 格式化用户信息</span>
    <span class="keyword">const</span> formattedUser = formatDingtalkUser(dingtalkUserInfo)

    <span class="comment">// 调用后端API</span>
    <span class="keyword">const</span> response = <span class="keyword">await</span> api.auth.dingtalkLogin({
      dingtalkToken,
      userInfo: formattedUser
    })

    <span class="keyword">if</span> (response.success) {
      <span class="keyword">const</span> user = {
        username: response.data.username,
        role: response.data.role,
        token: response.data.token,
        authType: <span class="string">'dingtalk'</span>
      }

      localStorage.setItem(<span class="string">'user'</span>, JSON.stringify(user))
      commit(<span class="string">'SET_USER'</span>, user)
      commit(<span class="string">'SET_AUTHENTICATED'</span>, <span class="keyword">true</span>)

      <span class="keyword">return</span> user
    }
  } <span class="keyword">catch</span> (error) {
    commit(<span class="string">'SET_USER'</span>, <span class="keyword">null</span>)
    commit(<span class="string">'SET_AUTHENTICATED'</span>, <span class="keyword">false</span>)
    <span class="keyword">throw</span> error
  } <span class="keyword">finally</span> {
    commit(<span class="string">'SET_LOADING'</span>, <span class="keyword">false</span>)
  }
}
                </div>
            </div>

            <el-alert 
                title="集成完成" 
                type="success" 
                description="钉钉OAuth2认证功能已成功集成到Vue项目中，用户可以通过钉钉账号快速登录系统。"
                show-icon
                :closable="false"
                style="margin-top: 30px;">
            </el-alert>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue
        const { ElMessage } = ElementPlus

        createApp({
            setup() {
                const loginForm = ref({
                    username: '',
                    password: ''
                })
                
                const dingtalkLoading = ref(false)

                const handleLogin = () => {
                    if (!loginForm.value.username || !loginForm.value.password) {
                        ElMessage.warning('请输入用户名和密码')
                        return
                    }
                    ElMessage.success('普通登录功能演示')
                }

                const handleDingtalkLogin = () => {
                    dingtalkLoading.value = true
                    ElMessage.info('正在跳转到钉钉授权页面...')
                    
                    // 模拟跳转延迟
                    setTimeout(() => {
                        dingtalkLoading.value = false
                        ElMessage.success('钉钉OAuth2认证流程演示完成！')
                    }, 2000)
                }

                return {
                    loginForm,
                    dingtalkLoading,
                    handleLogin,
                    handleDingtalkLogin
                }
            }
        }).use(ElementPlus).mount('#app')
    </script>
</body>
</html>
