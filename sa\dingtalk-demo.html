<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉OAuth2认证演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .demo-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #666;
            font-size: 16px;
        }

        .feature-list {
            margin: 30px 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }

        .feature-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            color: #2196F3;
        }

        .feature-text {
            flex: 1;
        }

        .feature-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .feature-desc {
            color: #666;
            font-size: 14px;
        }

        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #e0e0e0;
            border-radius: 8px;
            text-align: center;
        }

        .demo-button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .demo-button:hover {
            background: #1976D2;
            transform: translateY(-2px);
        }

        .demo-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .code-section {
            margin: 30px 0;
        }

        .code-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .code-block {
            background: #f5f5f5;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
        }

        .status-info {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
        }

        .status-success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }

        .status-error {
            background: #ffeaea;
            border: 1px solid #f44336;
            color: #c62828;
        }

        .status-info-default {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1565c0;
        }

        .file-structure {
            margin: 20px 0;
        }

        .file-tree {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="header">
            <h1>🔐 钉钉OAuth2认证集成</h1>
            <p>图书管理系统 - 钉钉登录功能演示</p>
        </div>

        <div class="feature-list">
            <div class="feature-item">
                <svg class="feature-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <div class="feature-text">
                    <div class="feature-title">OAuth2标准协议</div>
                    <div class="feature-desc">基于OAuth2.0标准实现安全认证</div>
                </div>
            </div>

            <div class="feature-item">
                <svg class="feature-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                <div class="feature-text">
                    <div class="feature-title">无缝集成</div>
                    <div class="feature-desc">与现有Vue3 + Element Plus架构完美融合</div>
                </div>
            </div>

            <div class="feature-item">
                <svg class="feature-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
                </svg>
                <div class="feature-text">
                    <div class="feature-title">安全可靠</div>
                    <div class="feature-desc">CSRF防护、令牌管理、错误处理</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🚀 功能演示</h3>
            <p>点击下方按钮体验钉钉登录功能</p>
            <button class="demo-button" onclick="simulateDingtalkLogin()">
                模拟钉钉登录
            </button>
            <button class="demo-button" onclick="showFileStructure()">
                查看文件结构
            </button>
            <button class="demo-button" onclick="showConfigExample()">
                配置示例
            </button>
        </div>

        <div id="status-display" class="status-info status-info-default">
            <strong>状态：</strong>钉钉OAuth2认证模块已集成完成，等待配置和测试
        </div>

        <div class="code-section">
            <div class="code-title">📁 已创建的文件：</div>
            <div class="file-structure">
                <div class="file-tree">
sa/src/
├── config/
│   └── dingtalkConfig.js          # 钉钉OAuth2配置
├── services/
│   └── dingtalkAuth.js            # 钉钉认证服务
├── utils/
│   └── dingtalkUtils.js           # 钉钉工具函数
├── views/
│   ├── Login.vue                  # 登录页面（已修改）
│   └── DingtalkCallback.vue       # 钉钉回调处理页面
├── store/modules/
│   └── auth.js                    # 认证模块（已修改）
├── api/
│   └── index.js                   # API配置（已修改）
└── router/
    └── index.js                   # 路由配置（已修改）
                </div>
            </div>
        </div>

        <div id="dynamic-content"></div>
    </div>

    <script>
        // 钉钉OAuth2配置
        const DINGTALK_CONFIG = {
            appId: 'dingoa8xvc6zrpzhfkqyv', // 示例AppId，请替换为您的真实AppId
            authUrl: 'https://login.dingtalk.com/oauth2/auth',
            redirectUri: window.location.origin + '/dingtalk-callback.html',
            scope: 'openid corpid',
            responseType: 'code',
            state: 'library_system_demo_' + Date.now()
        };

        function simulateDingtalkLogin() {
            const statusDiv = document.getElementById('status-display');
            statusDiv.className = 'status-info status-info-default';
            statusDiv.innerHTML = '<strong>准备中：</strong>正在生成钉钉授权URL...';

            // 生成真实的钉钉授权URL
            const authUrl = generateDingtalkAuthUrl();

            setTimeout(() => {
                statusDiv.className = 'status-info status-success';
                statusDiv.innerHTML = `
                    <strong>授权URL已生成：</strong><br>
                    <a href="${authUrl}" target="_blank" style="color: #1976D2; text-decoration: underline;">
                        点击此链接跳转到钉钉授权页面
                    </a><br>
                    <small style="color: #666;">注意：需要配置正确的AppId才能正常使用</small>
                `;
            }, 1000);
        }

        function generateDingtalkAuthUrl() {
            const params = new URLSearchParams({
                client_id: DINGTALK_CONFIG.appId,
                response_type: DINGTALK_CONFIG.responseType,
                scope: DINGTALK_CONFIG.scope,
                redirect_uri: DINGTALK_CONFIG.redirectUri,
                state: DINGTALK_CONFIG.state,
                prompt: 'consent'
            });

            return `${DINGTALK_CONFIG.authUrl}?${params.toString()}`;
        }

        function showFileStructure() {
            const content = document.getElementById('dynamic-content');
            content.innerHTML = `
                <div class="code-section">
                    <div class="code-title">🔧 核心功能模块：</div>
                    <div class="code-block">
<strong>1. 钉钉认证服务 (dingtalkAuth.js)</strong>
- generateAuthUrl(): 生成授权URL
- redirectToAuth(): 跳转到授权页面
- completeLogin(): 完成登录流程
- getAccessToken(): 获取访问令牌
- getUserInfo(): 获取用户信息

<strong>2. 工具函数 (dingtalkUtils.js)</strong>
- isDingtalkClient(): 检查是否在钉钉客户端
- checkDingtalkSupport(): 检查环境支持
- formatDingtalkUser(): 格式化用户信息
- validateDingtalkUserInfo(): 验证用户信息

<strong>3. 回调处理 (DingtalkCallback.vue)</strong>
- 处理钉钉OAuth2回调
- 错误处理和用户提示
- 自动跳转到主页面

<strong>4. 登录集成 (Login.vue)</strong>
- 添加钉钉登录按钮
- 环境检查和错误处理
- 用户友好的界面设计
                    </div>
                </div>
            `;
        }

        function showConfigExample() {
            const content = document.getElementById('dynamic-content');
            content.innerHTML = `
                <div class="code-section">
                    <div class="code-title">⚙️ 配置示例：</div>
                    <div class="code-block">
<strong>1. 修改 dingtalkConfig.js：</strong>
export const DINGTALK_CONFIG = {
  appId: 'your_app_id_here',        // 替换为您的钉钉应用ID
  appSecret: 'your_app_secret_here', // 替换为您的钉钉应用密钥
  redirectUri: window.location.origin + '/#/dingtalk/callback',
  // 其他配置保持默认
}

<strong>2. 后端API接口：</strong>
POST /api/auth/dingtalk/login
{
  "dingtalkToken": { "accessToken": "...", "refreshToken": "..." },
  "userInfo": { "username": "...", "nickname": "...", ... }
}

<strong>3. 钉钉开放平台配置：</strong>
- 创建企业内部应用或第三方应用
- 配置回调地址：http://your-domain/#/dingtalk/callback
- 获取AppId和AppSecret
- 设置应用权限和范围
                    </div>
                </div>
            `;
        }

        // 页面加载时显示欢迎信息
        window.onload = function() {
            setTimeout(() => {
                const statusDiv = document.getElementById('status-display');
                statusDiv.className = 'status-info status-success';
                statusDiv.innerHTML = '<strong>就绪：</strong>钉钉OAuth2认证模块已成功集成到图书管理系统中！';
            }, 1000);
        };
    </script>
</body>
</html>
