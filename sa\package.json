{"name": "hznu-library-system", "version": "0.1.0", "description": "杭师大图书管理系统 - 基于Vue.js和Electron的桌面应用", "author": "HZNU Student", "private": true, "main": "electron.js", "homepage": "./", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "electron": ".\\node_modules\\.bin\\electron.cmd .", "electron-dev": "concurrently \"npm run serve -- --port 8082\" \"wait-on http://localhost:8082 --timeout 60000 --interval 1000 --verbose && cross-env NODE_ENV=development .\\node_modules\\.bin\\electron.cmd .\"", "electron-prod": "npm run build && cross-env NODE_ENV=production .\\node_modules\\.bin\\electron.cmd .", "dist": "npm run build && electron-builder", "start": "npm run electron-dev"}, "dependencies": {"axios": "^1.9.0", "core-js": "^3.6.5", "element-plus": "^2.9.10", "vue": "^3.0.0", "vue-router": "^4.5.1", "vuex": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.19", "@vue/cli-plugin-eslint": "~4.5.19", "@vue/cli-service": "~4.5.19", "@vue/compiler-sfc": "^3.0.0", "babel-eslint": "^10.1.0", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^36.3.1", "electron-builder": "^24.13.3", "electron-store": "^10.0.1", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "wait-on": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "build": {"appId": "com.hznu.library-system", "productName": "杭师大图书管理系统", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron.js", "preload.js", "node_modules/**/*", "package.json"], "win": {"target": "portable"}}}