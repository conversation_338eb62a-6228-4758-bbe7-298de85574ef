<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉登录回调处理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .callback-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        .loading {
            color: #2196F3;
            margin-bottom: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #2196F3;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success {
            color: #4CAF50;
        }

        .error {
            color: #f44336;
        }

        .info-box {
            background: #f5f5f5;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }

        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }

        .button:hover {
            background: #1976D2;
        }

        .button.secondary {
            background: #666;
        }

        .button.secondary:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="callback-container">
        <div id="loading-section">
            <div class="spinner"></div>
            <h2 class="loading">处理钉钉登录回调...</h2>
            <p>正在验证授权信息，请稍候</p>
        </div>

        <div id="result-section" style="display: none;">
            <h2 id="result-title"></h2>
            <p id="result-message"></p>
            <div id="result-details" class="info-box"></div>
            <div id="result-actions"></div>
        </div>
    </div>

    <script>
        // 钉钉OAuth2配置
        const DINGTALK_CONFIG = {
            tokenUrl: 'https://api.dingtalk.com/v1.0/oauth2/userAccessToken',
            userInfoUrl: 'https://api.dingtalk.com/v1.0/contact/users/me',
            appId: 'dingoa8xvc6zrpzhfkqyv', // 示例AppId
            appSecret: 'your_app_secret_here' // 需要配置真实的AppSecret
        };

        // 解析URL参数
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                code: params.get('code'),
                state: params.get('state'),
                error: params.get('error'),
                error_description: params.get('error_description')
            };
        }

        // 显示结果
        function showResult(type, title, message, details = '', actions = '') {
            document.getElementById('loading-section').style.display = 'none';
            document.getElementById('result-section').style.display = 'block';
            
            const titleElement = document.getElementById('result-title');
            const messageElement = document.getElementById('result-message');
            const detailsElement = document.getElementById('result-details');
            const actionsElement = document.getElementById('result-actions');
            
            titleElement.className = type;
            titleElement.textContent = title;
            messageElement.textContent = message;
            detailsElement.innerHTML = details;
            actionsElement.innerHTML = actions;
        }

        // 模拟获取访问令牌（实际需要后端处理）
        async function getAccessToken(code) {
            // 注意：在实际应用中，这个请求应该在后端进行，因为需要AppSecret
            // 这里只是演示流程
            try {
                const response = await fetch(DINGTALK_CONFIG.tokenUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        clientId: DINGTALK_CONFIG.appId,
                        clientSecret: DINGTALK_CONFIG.appSecret,
                        code: code,
                        grantType: 'authorization_code'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                throw new Error(`获取访问令牌失败: ${error.message}`);
            }
        }

        // 模拟获取用户信息
        async function getUserInfo(accessToken) {
            try {
                const response = await fetch(DINGTALK_CONFIG.userInfoUrl, {
                    headers: {
                        'x-acs-dingtalk-access-token': accessToken
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                throw new Error(`获取用户信息失败: ${error.message}`);
            }
        }

        // 处理钉钉OAuth2回调
        async function handleDingtalkCallback() {
            const params = getUrlParams();
            
            // 检查是否有错误
            if (params.error) {
                const errorMessages = {
                    'access_denied': '用户拒绝了授权请求',
                    'invalid_request': '请求参数错误',
                    'unauthorized_client': '客户端未授权',
                    'unsupported_response_type': '不支持的响应类型',
                    'invalid_scope': '无效的权限范围',
                    'server_error': '服务器内部错误',
                    'temporarily_unavailable': '服务暂时不可用'
                };
                
                const errorMessage = errorMessages[params.error] || params.error_description || '未知错误';
                
                showResult(
                    'error',
                    '授权失败',
                    errorMessage,
                    `<strong>错误代码:</strong> ${params.error}<br>
                     <strong>错误描述:</strong> ${params.error_description || '无'}`,
                    `<a href="javascript:history.back()" class="button secondary">返回</a>
                     <a href="/dingtalk-demo.html" class="button">重新尝试</a>`
                );
                return;
            }

            // 检查必需参数
            if (!params.code) {
                showResult(
                    'error',
                    '参数错误',
                    '未收到授权码，请重新进行授权',
                    '<strong>缺少参数:</strong> code',
                    `<a href="/dingtalk-demo.html" class="button">重新授权</a>`
                );
                return;
            }

            if (!params.state) {
                showResult(
                    'error',
                    '安全验证失败',
                    '缺少状态参数，可能存在安全风险',
                    '<strong>缺少参数:</strong> state',
                    `<a href="/dingtalk-demo.html" class="button">重新授权</a>`
                );
                return;
            }

            // 显示接收到的参数
            const receivedParams = `
                <strong>授权码:</strong> ${params.code.substring(0, 20)}...<br>
                <strong>状态参数:</strong> ${params.state}<br>
                <strong>接收时间:</strong> ${new Date().toLocaleString()}
            `;

            try {
                // 在实际应用中，这些步骤应该在后端完成
                showResult(
                    'success',
                    '授权成功',
                    '已成功接收到钉钉授权码，正在处理...',
                    receivedParams + '<br><br><strong>注意:</strong> 在实际应用中，后续的令牌交换和用户信息获取应该在后端进行，以保护AppSecret的安全性。',
                    `<a href="/dingtalk-demo.html" class="button">返回演示页面</a>
                     <a href="javascript:location.reload()" class="button secondary">刷新页面</a>`
                );

                // 模拟后端处理流程（仅用于演示）
                setTimeout(() => {
                    const simulatedUserInfo = {
                        nick: '演示用户',
                        unionId: 'demo_union_id_' + Date.now(),
                        openId: 'demo_open_id_' + Date.now(),
                        avatarUrl: 'https://via.placeholder.com/64',
                        email: '<EMAIL>'
                    };

                    const finalDetails = receivedParams + `
                        <br><br><strong>模拟用户信息:</strong><br>
                        <strong>昵称:</strong> ${simulatedUserInfo.nick}<br>
                        <strong>UnionId:</strong> ${simulatedUserInfo.unionId}<br>
                        <strong>OpenId:</strong> ${simulatedUserInfo.openId}<br>
                        <strong>邮箱:</strong> ${simulatedUserInfo.email}<br>
                        <br><strong>状态:</strong> 模拟登录成功，用户信息已同步到系统
                    `;

                    showResult(
                        'success',
                        '登录完成',
                        '钉钉OAuth2认证流程演示完成！',
                        finalDetails,
                        `<a href="/dingtalk-demo.html" class="button">返回演示页面</a>`
                    );
                }, 2000);

            } catch (error) {
                showResult(
                    'error',
                    '处理失败',
                    error.message,
                    receivedParams + `<br><br><strong>错误详情:</strong> ${error.stack || error.message}`,
                    `<a href="/dingtalk-demo.html" class="button">重新尝试</a>`
                );
            }
        }

        // 页面加载时开始处理回调
        window.onload = function() {
            // 延迟一点时间以显示加载动画
            setTimeout(handleDingtalkCallback, 1000);
        };
    </script>
</body>
</html>
