# 钉钉OAuth2配置指南

## 📋 您的应用信息

根据您提供的钉钉应用凭证：

- **App ID**: `4e20ad14-0ba6-4efe-8d24-a5229ed88da8`
- **App Secret**: `q9JpxdnfFn3vHb68F__f4QA8t8YDl9dY0K5Zm6Hbeig3qHR0N4YCOjvdbuLyjsrK`
- **原企业内部应用AgentId**: `3902560951`

## 🔧 钉钉开放平台配置步骤

### 1. 登录钉钉开放平台
访问：https://open.dingtalk.com/

### 2. 配置回调地址
在您的应用设置中，需要配置以下回调地址：

**开发环境回调地址：**
```
http://localhost:8080/dingtalk-callback.html
```

**生产环境回调地址：**
```
https://your-domain.com/dingtalk-callback.html
```

### 3. 设置应用权限
确保您的应用具有以下权限：
- `openid` - 获取用户唯一标识
- `corpid` - 获取企业ID

### 4. 获取授权范围
当前配置的授权范围：`openid corpid`

## 🚀 测试步骤

### 步骤1：打开测试工具
访问：`file:///您的路径/sa/dingtalk-test.html`

### 步骤2：验证配置
- App ID 已自动填入：`4e20ad14-0ba6-4efe-8d24-a5229ed88da8`
- 回调地址会自动生成
- 点击"更新配置"按钮

### 步骤3：生成授权URL
点击"生成授权URL"按钮，系统会生成类似以下的URL：
```
https://login.dingtalk.com/oauth2/auth?client_id=4e20ad14-0ba6-4efe-8d24-a5229ed88da8&response_type=code&scope=openid+corpid&redirect_uri=http%3A//localhost%3A8080/dingtalk-callback.html&state=test_1234567890_abcdef&prompt=consent
```

### 步骤4：开始认证测试
点击"开始钉钉认证"按钮，系统会跳转到钉钉授权页面。

## ⚠️ 重要注意事项

### 1. 回调地址配置
确保在钉钉开放平台中配置的回调地址与测试工具中显示的回调地址完全一致。

### 2. HTTPS要求
生产环境必须使用HTTPS协议，HTTP协议仅适用于开发测试。

### 3. 域名白名单
某些钉钉应用类型可能需要配置域名白名单。

### 4. AppSecret安全
- AppSecret不应在前端代码中暴露
- 令牌交换应在后端服务器进行
- 当前测试工具仅用于验证OAuth2流程

## 🔍 故障排除

### 问题1：授权页面显示"应用不存在"
**解决方案：**
- 检查App ID是否正确
- 确认应用状态是否为"已发布"

### 问题2：回调时显示"redirect_uri不匹配"
**解决方案：**
- 检查钉钉开放平台中配置的回调地址
- 确保回调地址完全匹配（包括协议、域名、端口、路径）

### 问题3：获取不到用户信息
**解决方案：**
- 检查应用权限配置
- 确认授权范围是否正确
- 验证AppSecret是否正确

## 📞 技术支持

如果遇到问题，可以：
1. 查看钉钉开放平台文档：https://open.dingtalk.com/document/
2. 检查浏览器开发者工具的控制台错误
3. 使用测试工具的"检查回调参数"功能

## 🎯 下一步

配置完成后，您可以：
1. 在Vue项目中集成钉钉登录功能
2. 实现后端API接口处理令牌交换
3. 完善用户信息同步逻辑
4. 部署到生产环境

---

**配置文件位置：**
- 主配置：`sa/src/config/dingtalkConfig.js`
- 测试工具：`sa/dingtalk-test.html`
- 回调处理：`sa/dingtalk-callback.html`
