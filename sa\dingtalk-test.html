<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉OAuth2认证测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }

        .section h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input:focus {
            outline: none;
            border-color: #2196F3;
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
        }

        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .button:hover {
            background: #1976D2;
            transform: translateY(-1px);
        }

        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .button.secondary {
            background: #666;
        }

        .button.secondary:hover {
            background: #555;
        }

        .button.success {
            background: #4CAF50;
        }

        .button.success:hover {
            background: #45a049;
        }

        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-size: 14px;
        }

        .status.info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1565c0;
        }

        .status.success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }

        .status.error {
            background: #ffeaea;
            border: 1px solid #f44336;
            color: #c62828;
        }

        .status.warning {
            background: #fff3e0;
            border: 1px solid #ff9800;
            color: #ef6c00;
        }

        .code-block {
            background: #f5f5f5;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
            background: #f8f9fa;
        }

        .step-number {
            background: #2196F3;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
            flex-shrink: 0;
        }

        .step.completed .step-number {
            background: #4CAF50;
        }

        .step.error .step-number {
            background: #f44336;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 钉钉OAuth2认证测试工具</h1>
            <p>测试和验证钉钉OAuth2认证流程</p>
        </div>

        <div class="content">
            <!-- 配置部分 -->
            <div class="section">
                <h3>📋 配置信息</h3>
                <div class="form-group">
                    <label for="appId">钉钉应用ID (AppId):</label>
                    <input type="text" id="appId" value="4e20ad14-0ba6-4efe-8d24-a5229ed88da8" placeholder="请输入您的钉钉应用ID">
                </div>
                <div class="form-group">
                    <label for="redirectUri">回调地址:</label>
                    <input type="text" id="redirectUri" readonly>
                </div>
                <div class="form-group">
                    <label for="scope">授权范围:</label>
                    <input type="text" id="scope" value="openid corpid" readonly>
                </div>
                <button class="button" onclick="updateConfig()">更新配置</button>
                <button class="button secondary" onclick="resetConfig()">重置配置</button>
            </div>

            <!-- 测试步骤 -->
            <div class="section">
                <h3>🚀 测试步骤</h3>
                <div id="step1" class="step">
                    <div class="step-number">1</div>
                    <div>生成授权URL</div>
                </div>
                <div id="step2" class="step">
                    <div class="step-number">2</div>
                    <div>跳转到钉钉授权页面</div>
                </div>
                <div id="step3" class="step">
                    <div class="step-number">3</div>
                    <div>用户授权并回调</div>
                </div>
                <div id="step4" class="step">
                    <div class="step-number">4</div>
                    <div>处理回调并获取用户信息</div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="section">
                <h3>🎯 操作</h3>
                <button class="button" onclick="generateAuthUrl()">生成授权URL</button>
                <button class="button success" onclick="startDingtalkAuth()" id="authButton" disabled>开始钉钉认证</button>
                <button class="button secondary" onclick="checkCallback()">检查回调参数</button>
                <button class="button secondary" onclick="clearResults()">清除结果</button>
            </div>

            <!-- 结果显示 -->
            <div class="section">
                <h3>📊 结果</h3>
                <div id="statusDisplay" class="status info">
                    等待操作...
                </div>
                <div id="resultDisplay" class="code-block hidden"></div>
            </div>

            <!-- 说明信息 -->
            <div class="section">
                <h3>ℹ️ 说明</h3>
                <div class="status warning">
                    <strong>注意事项：</strong><br>
                    1. 这是一个测试工具，用于验证钉钉OAuth2认证流程<br>
                    2. 需要配置正确的钉钉应用ID才能正常使用<br>
                    3. 回调地址需要在钉钉开放平台中正确配置<br>
                    4. 实际的令牌交换应该在后端进行，以保护AppSecret安全
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局配置
        let config = {
            appId: '4e20ad14-0ba6-4efe-8d24-a5229ed88da8',
            authUrl: 'https://login.dingtalk.com/oauth2/auth',
            redirectUri: window.location.origin + '/dingtalk-callback.html',
            scope: 'openid corpid',
            responseType: 'code',
            state: ''
        };

        // 初始化页面
        function initPage() {
            document.getElementById('redirectUri').value = config.redirectUri;
            updateStatus('info', '页面已加载，请配置钉钉应用信息');
            checkUrlParams();
        }

        // 更新配置
        function updateConfig() {
            config.appId = document.getElementById('appId').value.trim();
            config.scope = document.getElementById('scope').value.trim();

            if (!config.appId) {
                updateStatus('error', '请输入有效的钉钉应用ID');
                return;
            }

            updateStatus('success', '配置已更新');
            markStepCompleted('step1');
        }

        // 重置配置
        function resetConfig() {
            document.getElementById('appId').value = '4e20ad14-0ba6-4efe-8d24-a5229ed88da8';
            document.getElementById('scope').value = 'openid corpid';
            updateConfig();
        }

        // 生成授权URL
        function generateAuthUrl() {
            if (!config.appId) {
                updateStatus('error', '请先配置钉钉应用ID');
                return;
            }

            config.state = 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            const params = new URLSearchParams({
                client_id: config.appId,
                response_type: config.responseType,
                scope: config.scope,
                redirect_uri: config.redirectUri,
                state: config.state,
                prompt: 'consent'
            });

            const authUrl = `${config.authUrl}?${params.toString()}`;

            showResult(`授权URL已生成：\n${authUrl}\n\nState参数: ${config.state}`);
            updateStatus('success', '授权URL已生成，可以开始认证流程');

            document.getElementById('authButton').disabled = false;
            markStepCompleted('step1');
        }

        // 开始钉钉认证
        function startDingtalkAuth() {
            if (!config.state) {
                updateStatus('error', '请先生成授权URL');
                return;
            }

            const params = new URLSearchParams({
                client_id: config.appId,
                response_type: config.responseType,
                scope: config.scope,
                redirect_uri: config.redirectUri,
                state: config.state,
                prompt: 'consent'
            });

            const authUrl = `${config.authUrl}?${params.toString()}`;

            updateStatus('info', '正在跳转到钉钉授权页面...');
            markStepCompleted('step2');

            // 跳转到钉钉授权页面
            window.location.href = authUrl;
        }

        // 检查回调参数
        function checkCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const hashParams = new URLSearchParams(window.location.hash.substring(1));

            const params = {
                query: Object.fromEntries(urlParams),
                hash: Object.fromEntries(hashParams)
            };

            if (Object.keys(params.query).length === 0 && Object.keys(params.hash).length === 0) {
                updateStatus('info', '当前URL中没有检测到回调参数');
                showResult('当前URL: ' + window.location.href + '\n\n没有检测到钉钉回调参数');
            } else {
                updateStatus('success', '检测到回调参数');
                showResult('回调参数:\n' + JSON.stringify(params, null, 2));

                if (params.query.code || params.hash.code) {
                    markStepCompleted('step3');
                    markStepCompleted('step4');
                }
            }
        }

        // 检查URL参数
        function checkUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('code')) {
                updateStatus('success', '检测到钉钉回调，授权码: ' + urlParams.get('code').substring(0, 20) + '...');
                markStepCompleted('step3');
                markStepCompleted('step4');

                showResult(`钉钉回调成功！

授权码: ${urlParams.get('code')}
状态参数: ${urlParams.get('state')}
接收时间: ${new Date().toLocaleString()}

注意: 在实际应用中，需要使用这个授权码在后端换取访问令牌。`);
            }
        }

        // 更新状态显示
        function updateStatus(type, message) {
            const statusDiv = document.getElementById('statusDisplay');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
        }

        // 显示结果
        function showResult(content) {
            const resultDiv = document.getElementById('resultDisplay');
            resultDiv.textContent = content;
            resultDiv.classList.remove('hidden');
        }

        // 清除结果
        function clearResults() {
            document.getElementById('resultDisplay').classList.add('hidden');
            updateStatus('info', '结果已清除');

            // 重置步骤状态
            ['step1', 'step2', 'step3', 'step4'].forEach(stepId => {
                const step = document.getElementById(stepId);
                step.classList.remove('completed', 'error');
            });

            document.getElementById('authButton').disabled = true;
        }

        // 标记步骤完成
        function markStepCompleted(stepId) {
            document.getElementById(stepId).classList.add('completed');
        }

        // 标记步骤错误
        function markStepError(stepId) {
            document.getElementById(stepId).classList.add('error');
        }

        // 页面加载时初始化
        window.onload = initPage;
    </script>
</body>
</html>
