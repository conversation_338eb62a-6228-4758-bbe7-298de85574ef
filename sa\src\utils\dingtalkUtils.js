// 钉钉相关工具函数
import { DINGTALK_STORAGE_KEYS } from '@/config/dingtalkConfig'

/**
 * 检查是否在钉钉客户端中
 * @returns {boolean} 是否在钉钉客户端
 */
export function isDingtalkClient() {
  const userAgent = navigator.userAgent.toLowerCase()
  return userAgent.includes('dingtalk')
}

/**
 * 检查是否在移动设备上
 * @returns {boolean} 是否在移动设备
 */
export function isMobileDevice() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * 获取URL参数
 * @param {string} name 参数名
 * @param {string} url URL字符串，默认为当前页面URL
 * @returns {string|null} 参数值
 */
export function getUrlParameter(name, url = window.location.href) {
  const urlObj = new URL(url)
  return urlObj.searchParams.get(name)
}

/**
 * 解析URL hash中的参数
 * @param {string} hash hash字符串，默认为当前页面hash
 * @returns {Object} 参数对象
 */
export function parseHashParams(hash = window.location.hash) {
  const params = {}
  if (hash.includes('?')) {
    const queryString = hash.split('?')[1]
    const urlParams = new URLSearchParams(queryString)
    for (const [key, value] of urlParams) {
      params[key] = value
    }
  }
  return params
}

/**
 * 生成随机字符串（用于state参数）
 * @param {number} length 字符串长度
 * @returns {string} 随机字符串
 */
export function generateRandomString(length = 32) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 格式化钉钉用户信息为系统用户格式
 * @param {Object} dingtalkUser 钉钉用户信息
 * @returns {Object} 系统用户格式
 */
export function formatDingtalkUser(dingtalkUser) {
  return {
    username: dingtalkUser.nick || dingtalkUser.name || dingtalkUser.unionId,
    nickname: dingtalkUser.nick || dingtalkUser.name,
    avatar: dingtalkUser.avatarUrl,
    email: dingtalkUser.email,
    mobile: dingtalkUser.mobile,
    unionId: dingtalkUser.unionId,
    openId: dingtalkUser.openId,
    corpId: dingtalkUser.corpId,
    // 默认为普通用户角色
    role: 'ROLE_USER',
    // 标记为钉钉用户
    authType: 'dingtalk',
    // 其他信息
    realName: dingtalkUser.name,
    department: dingtalkUser.deptName,
    position: dingtalkUser.position
  }
}

/**
 * 验证钉钉用户信息完整性
 * @param {Object} userInfo 用户信息
 * @returns {Object} 验证结果
 */
export function validateDingtalkUserInfo(userInfo) {
  const errors = []
  
  if (!userInfo) {
    errors.push('用户信息为空')
    return { valid: false, errors }
  }

  // 检查必需字段
  if (!userInfo.unionId && !userInfo.openId) {
    errors.push('缺少用户唯一标识')
  }

  if (!userInfo.nick && !userInfo.name) {
    errors.push('缺少用户姓名')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 检查钉钉认证状态
 * @returns {Object} 认证状态信息
 */
export function checkDingtalkAuthStatus() {
  const accessToken = localStorage.getItem(DINGTALK_STORAGE_KEYS.ACCESS_TOKEN)
  const userInfo = localStorage.getItem(DINGTALK_STORAGE_KEYS.USER_INFO)
  
  return {
    isAuthenticated: !!(accessToken && userInfo),
    hasToken: !!accessToken,
    hasUserInfo: !!userInfo,
    userInfo: userInfo ? JSON.parse(userInfo) : null
  }
}

/**
 * 清理过期的认证数据
 */
export function cleanupExpiredAuthData() {
  // 这里可以添加令牌过期检查逻辑
  // 目前简单实现，实际应用中应该检查令牌的过期时间
  const authStatus = checkDingtalkAuthStatus()
  
  if (authStatus.hasToken && !authStatus.hasUserInfo) {
    // 如果有令牌但没有用户信息，清理数据
    localStorage.removeItem(DINGTALK_STORAGE_KEYS.ACCESS_TOKEN)
    localStorage.removeItem(DINGTALK_STORAGE_KEYS.REFRESH_TOKEN)
  }
}

/**
 * 处理钉钉登录错误
 * @param {string} error 错误代码
 * @returns {string} 用户友好的错误信息
 */
export function handleDingtalkLoginError(error) {
  const errorMessages = {
    'access_denied': '您拒绝了授权请求',
    'invalid_request': '登录请求参数错误',
    'invalid_client': '应用配置错误',
    'invalid_grant': '授权码无效或已过期',
    'unauthorized_client': '应用未获得授权',
    'unsupported_grant_type': '不支持的授权类型',
    'invalid_scope': '请求的权限范围无效',
    'temporarily_unavailable': '钉钉服务暂时不可用，请稍后重试'
  }

  return errorMessages[error] || `登录失败: ${error}`
}

/**
 * 检查是否支持钉钉登录
 * @returns {Object} 支持状态信息
 */
export function checkDingtalkSupport() {
  const isSupported = typeof window !== 'undefined' && 
                     typeof localStorage !== 'undefined' &&
                     typeof URLSearchParams !== 'undefined'

  const warnings = []
  
  if (!isSupported) {
    warnings.push('当前环境不支持钉钉登录')
  }

  // 检查是否在HTTPS环境（生产环境要求）
  if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
    warnings.push('钉钉登录需要HTTPS环境')
  }

  return {
    isSupported,
    warnings
  }
}

/**
 * 获取钉钉登录按钮样式类
 * @param {string} theme 主题类型 ('default', 'primary', 'success')
 * @returns {string} CSS类名
 */
export function getDingtalkButtonClass(theme = 'default') {
  const baseClass = 'dingtalk-login-btn'
  const themeClass = `dingtalk-login-btn--${theme}`
  return `${baseClass} ${themeClass}`
}

/**
 * 记录钉钉登录事件（用于分析）
 * @param {string} event 事件类型
 * @param {Object} data 事件数据
 */
export function logDingtalkEvent(event, data = {}) {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[钉钉登录] ${event}:`, data)
  }
  
  // 这里可以添加实际的事件上报逻辑
  // 例如发送到分析服务
}

/**
 * 创建钉钉登录状态监听器
 * @param {Function} callback 状态变化回调
 * @returns {Function} 取消监听函数
 */
export function createDingtalkAuthListener(callback) {
  const handleStorageChange = (event) => {
    if (event.key === DINGTALK_STORAGE_KEYS.ACCESS_TOKEN || 
        event.key === DINGTALK_STORAGE_KEYS.USER_INFO) {
      const authStatus = checkDingtalkAuthStatus()
      callback(authStatus)
    }
  }

  window.addEventListener('storage', handleStorageChange)
  
  // 返回取消监听函数
  return () => {
    window.removeEventListener('storage', handleStorageChange)
  }
}
