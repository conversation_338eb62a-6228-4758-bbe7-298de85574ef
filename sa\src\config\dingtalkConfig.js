// 钉钉OAuth2配置
export const DINGTALK_CONFIG = {
  // 钉钉应用配置
  appId: 'ding58u4dfzuyafocfi4', // 请替换为您的钉钉应用AppId
  appSecret: 'q9JpxdnfFn3vHb68F__f4QA8t8YDl9dY0K5Zm6Hbeig3qHR0N4YCOjvdbuLyjsrK', // 请替换为您的钉钉应用AppSecret
  
  // OAuth2授权相关配置
  authUrl: 'https://login.dingtalk.com/oauth2/auth',
  tokenUrl: 'https://api.dingtalk.com/v1.0/oauth2/userAccessToken',
  userInfoUrl: 'https://api.dingtalk.com/v1.0/contact/users/me',
  
  // 回调地址配置
  redirectUri: window.location.origin + '/#/dingtalk/callback',
  
  // 授权范围
  scope: 'openid corpid',
  
  // 响应类型
  responseType: 'code',
  
  // 状态参数（用于防止CSRF攻击）
  state: 'library_system_auth',
  
  // 提示类型
  prompt: 'consent'
}

// 钉钉API配置
export const DINGTALK_API_CONFIG = {
  baseUrl: 'https://api.dingtalk.com',
  version: 'v1.0',
  timeout: 10000
}

// 错误码映射
export const DINGTALK_ERROR_CODES = {
  'invalid_request': '请求参数错误',
  'invalid_client': '客户端认证失败',
  'invalid_grant': '授权码无效或已过期',
  'unauthorized_client': '客户端未授权',
  'unsupported_grant_type': '不支持的授权类型',
  'invalid_scope': '请求的权限范围无效',
  'access_denied': '用户拒绝授权',
  'temporarily_unavailable': '服务暂时不可用'
}

// 本地存储键名
export const DINGTALK_STORAGE_KEYS = {
  ACCESS_TOKEN: 'dingtalk_access_token',
  REFRESH_TOKEN: 'dingtalk_refresh_token',
  USER_INFO: 'dingtalk_user_info',
  AUTH_STATE: 'dingtalk_auth_state'
}
