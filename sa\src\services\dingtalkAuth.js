// 钉钉OAuth2认证服务
import axios from 'axios'
import { 
  DINGTALK_CONFIG, 
  DINGTALK_API_CONFIG, 
  DINGTALK_ERROR_CODES,
  DINGTALK_STORAGE_KEYS 
} from '@/config/dingtalkConfig'

class DingtalkAuthService {
  constructor() {
    // 创建专用的axios实例用于钉钉API调用
    this.apiClient = axios.create({
      baseURL: DINGTALK_API_CONFIG.baseUrl,
      timeout: DINGTALK_API_CONFIG.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // 响应拦截器
    this.apiClient.interceptors.response.use(
      response => response.data,
      error => {
        console.error('钉钉API请求错误:', error)
        return Promise.reject(this.handleError(error))
      }
    )
  }

  /**
   * 生成钉钉OAuth2授权URL
   * @returns {string} 授权URL
   */
  generateAuthUrl() {
    const params = new URLSearchParams({
      client_id: DINGTALK_CONFIG.appId,
      response_type: DINGTALK_CONFIG.responseType,
      scope: DINGTALK_CONFIG.scope,
      redirect_uri: DINGTALK_CONFIG.redirectUri,
      state: DINGTALK_CONFIG.state,
      prompt: DINGTALK_CONFIG.prompt
    })

    // 保存state到本地存储，用于验证回调
    localStorage.setItem(DINGTALK_STORAGE_KEYS.AUTH_STATE, DINGTALK_CONFIG.state)

    return `${DINGTALK_CONFIG.authUrl}?${params.toString()}`
  }

  /**
   * 跳转到钉钉授权页面
   */
  redirectToAuth() {
    const authUrl = this.generateAuthUrl()
    console.log('跳转到钉钉授权页面:', authUrl)
    window.location.href = authUrl
  }

  /**
   * 验证回调状态参数
   * @param {string} state 回调中的state参数
   * @returns {boolean} 验证结果
   */
  validateState(state) {
    const storedState = localStorage.getItem(DINGTALK_STORAGE_KEYS.AUTH_STATE)
    return state === storedState
  }

  /**
   * 使用授权码获取访问令牌
   * @param {string} code 授权码
   * @returns {Promise<Object>} 令牌信息
   */
  async getAccessToken(code) {
    try {
      const response = await this.apiClient.post(`/${DINGTALK_API_CONFIG.version}/oauth2/userAccessToken`, {
        clientId: DINGTALK_CONFIG.appId,
        clientSecret: DINGTALK_CONFIG.appSecret,
        code: code,
        grantType: 'authorization_code'
      })

      if (response.accessToken) {
        // 保存令牌到本地存储
        localStorage.setItem(DINGTALK_STORAGE_KEYS.ACCESS_TOKEN, response.accessToken)
        if (response.refreshToken) {
          localStorage.setItem(DINGTALK_STORAGE_KEYS.REFRESH_TOKEN, response.refreshToken)
        }
        
        return {
          success: true,
          data: response
        }
      } else {
        throw new Error('获取访问令牌失败')
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取用户信息
   * @param {string} accessToken 访问令牌
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(accessToken) {
    try {
      const response = await this.apiClient.get(`/${DINGTALK_API_CONFIG.version}/contact/users/me`, {
        headers: {
          'x-acs-dingtalk-access-token': accessToken
        }
      })

      if (response) {
        // 保存用户信息到本地存储
        localStorage.setItem(DINGTALK_STORAGE_KEYS.USER_INFO, JSON.stringify(response))
        
        return {
          success: true,
          data: response
        }
      } else {
        throw new Error('获取用户信息失败')
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 刷新访问令牌
   * @param {string} refreshToken 刷新令牌
   * @returns {Promise<Object>} 新的令牌信息
   */
  async refreshAccessToken(refreshToken) {
    try {
      const response = await this.apiClient.post(`/${DINGTALK_API_CONFIG.version}/oauth2/userAccessToken`, {
        clientId: DINGTALK_CONFIG.appId,
        clientSecret: DINGTALK_CONFIG.appSecret,
        refreshToken: refreshToken,
        grantType: 'refresh_token'
      })

      if (response.accessToken) {
        // 更新本地存储的令牌
        localStorage.setItem(DINGTALK_STORAGE_KEYS.ACCESS_TOKEN, response.accessToken)
        if (response.refreshToken) {
          localStorage.setItem(DINGTALK_STORAGE_KEYS.REFRESH_TOKEN, response.refreshToken)
        }
        
        return {
          success: true,
          data: response
        }
      } else {
        throw new Error('刷新访问令牌失败')
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 清除本地存储的钉钉认证信息
   */
  clearAuthData() {
    localStorage.removeItem(DINGTALK_STORAGE_KEYS.ACCESS_TOKEN)
    localStorage.removeItem(DINGTALK_STORAGE_KEYS.REFRESH_TOKEN)
    localStorage.removeItem(DINGTALK_STORAGE_KEYS.USER_INFO)
    localStorage.removeItem(DINGTALK_STORAGE_KEYS.AUTH_STATE)
  }

  /**
   * 获取本地存储的访问令牌
   * @returns {string|null} 访问令牌
   */
  getStoredAccessToken() {
    return localStorage.getItem(DINGTALK_STORAGE_KEYS.ACCESS_TOKEN)
  }

  /**
   * 获取本地存储的用户信息
   * @returns {Object|null} 用户信息
   */
  getStoredUserInfo() {
    const userInfo = localStorage.getItem(DINGTALK_STORAGE_KEYS.USER_INFO)
    return userInfo ? JSON.parse(userInfo) : null
  }

  /**
   * 处理API错误
   * @param {Error} error 错误对象
   * @returns {Error} 处理后的错误
   */
  handleError(error) {
    if (error.response?.data?.code) {
      const errorCode = error.response.data.code
      const errorMessage = DINGTALK_ERROR_CODES[errorCode] || error.response.data.message || '未知错误'
      return new Error(errorMessage)
    }
    return error
  }

  /**
   * 完整的钉钉OAuth2登录流程
   * @param {string} code 授权码
   * @param {string} state 状态参数
   * @returns {Promise<Object>} 登录结果
   */
  async completeLogin(code, state) {
    try {
      // 1. 验证state参数
      if (!this.validateState(state)) {
        throw new Error('状态参数验证失败，可能存在安全风险')
      }

      // 2. 获取访问令牌
      const tokenResult = await this.getAccessToken(code)
      if (!tokenResult.success) {
        throw new Error(tokenResult.error)
      }

      // 3. 获取用户信息
      const userInfoResult = await this.getUserInfo(tokenResult.data.accessToken)
      if (!userInfoResult.success) {
        throw new Error(userInfoResult.error)
      }

      // 4. 清除临时状态
      localStorage.removeItem(DINGTALK_STORAGE_KEYS.AUTH_STATE)

      return {
        success: true,
        data: {
          token: tokenResult.data,
          userInfo: userInfoResult.data
        }
      }
    } catch (error) {
      this.clearAuthData()
      return {
        success: false,
        error: error.message
      }
    }
  }
}

// 导出单例实例
export default new DingtalkAuthService()
